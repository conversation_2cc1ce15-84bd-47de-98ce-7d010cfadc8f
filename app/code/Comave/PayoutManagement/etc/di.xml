<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="upcoming_payouts_listing_data_source" xsi:type="string">Comave\PayoutManagement\Model\ResourceModel\Payout\Grid\Collection</item>
                <item name="completed_payouts_listing_data_source" xsi:type="string">Comave\PayoutManagement\Model\ResourceModel\Payout\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    
    <virtualType name="ComaveUpcomingPayoutsGridFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>
    
    <virtualType name="ComaveCompletedPayoutsGridFilterPool" type="Magento\Framework\View\Element\UiComponent\DataProvider\FilterPool">
        <arguments>
            <argument name="appliers" xsi:type="array">
                <item name="regular" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\RegularFilter</item>
                <item name="fulltext" xsi:type="object">Magento\Framework\View\Element\UiComponent\DataProvider\FulltextFilter</item>
            </argument>
        </arguments>
    </virtualType>
    
    <type name="Comave\PayoutManagement\Ui\DataProvider\UpcomingPayoutsDataProvider">
        <arguments>
            <argument name="collectionFactory" xsi:type="object">Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory</argument>
        </arguments>
    </type>

    <type name="Comave\PayoutManagement\Ui\DataProvider\CompletedPayoutsDataProvider">
        <arguments>
            <argument name="collectionFactory" xsi:type="object">Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory</argument>
        </arguments>
    </type>
    
</config>
