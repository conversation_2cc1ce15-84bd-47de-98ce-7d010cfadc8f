<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class LastSyncService
{
    public function __construct(
        private readonly CollectionFactory $payoutCollectionFactory,
        private readonly TimezoneInterface $timezone
    ) {}

    /**
     * Get the latest sync timestamp from all payouts
     *
     * @return string|null
     */
    public function getLastSyncTimestamp(): ?string
    {
        $collection = $this->payoutCollectionFactory->create();
        $collection->addFieldToFilter('last_sync_at', ['notnull' => true]);
        $collection->setOrder('last_sync_at', 'DESC');
        $collection->setPageSize(1);
        
        $latestPayout = $collection->getFirstItem();
        
        if ($latestPayout->getId()) {
            return $latestPayout->getLastSyncAt();
        }
        
        return null;
    }

    /**
     * Get formatted last sync timestamp for display
     *
     * @return string
     */
    public function getFormattedLastSync(): string
    {
        $lastSync = $this->getLastSyncTimestamp();
        
        if (!$lastSync) {
            return __('Never synced')->render();
        }
        
        try {
            $date = $this->timezone->date($lastSync);
            return $date->format('M j, Y g:i A');
        } catch (\Exception $e) {
            return __('Invalid date')->render();
        }
    }
}
