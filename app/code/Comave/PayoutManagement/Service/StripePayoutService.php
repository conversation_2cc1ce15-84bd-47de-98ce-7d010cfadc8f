<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Service;

use Comave\SellerPayouts\Helper\Data as SellerPayoutsHelper;
use Comave\SellerPayouts\Helper\Payout\Collections as PayoutCollections;
use Magento\Customer\Model\CustomerFactory;
use Psr\Log\LoggerInterface;
use Stripe\StripeClient;
use Stripe\Payout;
use Stripe\Exception\ApiErrorException;

class StripePayoutService
{
    private const PAYOUT_STATUS_MAPPING = [
        'pending' => 'pending',
        'in_transit' => 'in_transit',
        'paid' => 'paid',
        'failed' => 'failed'
    ];

    public function __construct(
        private readonly SellerPayoutsHelper $sellerPayoutsHelper,
        private readonly PayoutCollections $payoutCollections,
        private readonly CustomerFactory $customerFactory,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Get all payouts from Stripe for connected accounts
     */
    public function getAllPayouts(): array
    {
        try {
            $stripeClient = $this->getStripeClient();
            $connectedAccounts = $this->getConnectedAccounts();
            $allPayouts = [];

            foreach ($connectedAccounts as $account) {
                $payouts = $this->getPayoutsForAccount($stripeClient, $account);
                $allPayouts = array_merge($allPayouts, $payouts);
            }

            return $allPayouts;
        } catch (\Exception $e) {
            $this->logger->error('Error fetching payouts from Stripe', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get Stripe client instance
     *
     * @return StripeClient
     * @throws \Exception
     */
    private function getStripeClient(): StripeClient
    {
        $secretKey = $this->sellerPayoutsHelper->getDecryptedKey();
        if (empty($secretKey)) {
            throw new \Exception('Stripe secret key not configured');
        }

        return new StripeClient($secretKey);
    }

    /**
     * Get all connected Stripe accounts
     *
     * @return array
     */
    private function getConnectedAccounts(): array
    {
        try {
            $sellerCollection = $this->payoutCollections->getSellerCollection();
            $accounts = [];

            foreach ($sellerCollection as $seller) {
                if (!empty($seller->getData('stripe_client_id'))) {
                    $customer = $this->customerFactory->create()->load($seller->getSellerId());
                    $accounts[] = [
                        'seller_id' => $seller->getSellerId(),
                        'seller_name' => $customer->getFirstname() . ' ' . $customer->getLastname(),
                        'stripe_account_id' => $seller->getData('stripe_client_id')
                    ];
                }
            }

            return $accounts;
        } catch (\Exception $e) {
            $this->logger->error('Error getting connected accounts: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get payouts for specific Stripe account
     *
     * @param StripeClient $stripeClient
     * @param array $account
     * @return array
     */
    private function getPayoutsForAccount(StripeClient $stripeClient, array $account): array
    {
        try {
            $payouts = $stripeClient->payouts->all([
                'limit' => 100,
                'expand' => ['data.destination']
            ], [
                'stripe_account' => $account['stripe_account_id']
            ]);

            $formattedPayouts = [];
            foreach ($payouts->data as $payout) {
                $formattedPayouts[] = $this->formatPayoutData($payout, $account);
            }

            return $formattedPayouts;
        } catch (ApiErrorException $e) {
            $this->logger->info('Stripe API error fetching payouts', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getStripeCode(),
                'seller_id' => $account['seller_id']
            ]);
            return [];
        }
    }

    /**
     * Format payout data for consistent structure
     *
     * @param Payout $payout
     * @param array $account
     * @return array
     */
    private function formatPayoutData(Payout $payout, array $account): array
    {
        return [
            'stripe_payout_id' => $payout->id,
            'seller_id' => $account['seller_id'],
            'seller_name' => $account['seller_name'],
            'stripe_account_id' => $account['stripe_account_id'],
            'amount' => $payout->amount / 100, // Convert from cents
            'currency' => strtoupper($payout->currency),
            'status' => $this->mapStripeStatus($payout->status),
            'payment_method' => 'stripe',
            'scheduled_date' => $payout->arrival_date ? date('Y-m-d H:i:s', $payout->arrival_date) : null,
            'completion_date' => $payout->status === 'paid' ? date('Y-m-d H:i:s', $payout->created) : null,
            'created_at' => date('Y-m-d H:i:s', $payout->created),
            'last_sync_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Map Stripe payout status to our internal status
     *
     * @param string $stripeStatus
     * @return string
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return self::PAYOUT_STATUS_MAPPING[$stripeStatus] ?? 'pending';
    }
}
