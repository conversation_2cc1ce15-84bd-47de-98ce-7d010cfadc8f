<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Block\Adminhtml;

use Magento\Framework\View\Element\Template;
use Comave\PayoutManagement\Service\LastSyncService;

class LastSyncInfo extends Template
{
    public function __construct(
        Template\Context $context,
        private readonly LastSyncService $lastSyncService,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    public function getLastSyncText(): string
    {
        $lastSync = $this->lastSyncService->getFormattedLastSync();
        return __('Last sync: %1', $lastSync)->render();
    }
}
