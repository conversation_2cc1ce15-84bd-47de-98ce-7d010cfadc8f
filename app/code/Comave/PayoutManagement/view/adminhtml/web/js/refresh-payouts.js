define([
    'jquery',
    'Magento_Ui/js/modal/alert',
    'mage/translate',
    'loader'
], function ($, alert, $t) {
    'use strict';

    return {
        refresh: function(refreshUrl) {
            var body = $('body');
            body.trigger('processStart');

            var formKey = $('input[name="form_key"]').val();

            $.ajax({
                url: refreshUrl,
                type: 'POST',
                dataType: 'json',
                data: {
                    form_key: formKey
                },
                success: function(response) {
                    body.trigger('processStop');
                    
                    if (response.success) {
                        alert({
                            title: $t('Success'),
                            content: response.message,
                            actions: {
                                always: function() {
                                    window.location.reload();
                                }
                            }
                        });
                    } else {
                        alert({
                            title: $t('Error'),
                            content: response.message || $t('Failed to refresh payouts')
                        });
                    }
                },
                error: function(xhr, status, error) {
                    body.trigger('processStop');

                    var errorMessage = $t('An error occurred while refreshing payouts');

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            errorMessage = xhr.statusText || error || errorMessage;
                        }
                    }

                    alert({
                        title: $t('Error'),
                        content: errorMessage
                    });
                }
            });
        }
    };
});
