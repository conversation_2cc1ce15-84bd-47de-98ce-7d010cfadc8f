<?php

declare(strict_types=1);

namespace Comave\PayoutManagement\Model\Config\Backend;

use Magento\Framework\App\Config\Value;
use Magento\Framework\App\Config\ValueFactory;
use Magento\Framework\Model\Context;
use Magento\Framework\Registry;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Cache\TypeListInterface;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Framework\Data\Collection\AbstractDb;
use Magento\Framework\Exception\CouldNotSaveException;

class SyncFrequency extends Value
{
    private const CRON_STRING_PATH = 'crontab/comave_group/jobs/comave_payout_management_sync/schedule/cron_expr';
    private const CRON_MODEL_PATH = 'crontab/comave_group/jobs/comave_payout_management_sync/run/model';

    public function __construct(
        Context $context,
        Registry $registry,
        ScopeConfigInterface $config,
        TypeListInterface $cacheTypeList,
        private readonly ValueFactory $configValueFactory,
        ?AbstractResource $resource = null,
        ?AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $config, $cacheTypeList, $resource, $resourceCollection, $data);
    }

    /**
     * Update cron expression when frequency changes
     *
     * @return $this
     * @throws \Exception
     */
    public function afterSave(): self
    {
        $frequency = $this->getValue();

        if ($frequency) {
            try {
                $this->cleanupOldConfig();

                $this->configValueFactory->create()->load(
                    self::CRON_STRING_PATH,
                    'path'
                )->setValue(
                    $frequency
                )->setPath(
                    self::CRON_STRING_PATH
                )->save();

                $this->configValueFactory->create()->load(
                    self::CRON_MODEL_PATH,
                    'path'
                )->setValue(
                    'Comave\PayoutManagement\Cron\SyncPayouts::execute'
                )->setPath(
                    self::CRON_MODEL_PATH
                )->save();

                $this->_logger->info('Updated cron expression for payout sync', [
                    'frequency' => $frequency,
                    'cron_path' => self::CRON_STRING_PATH,
                    'group' => 'comave_group'
                ]);

            } catch (\Exception $e) {
                $this->_logger->error('Error updating cron expression: ' . $e->getMessage());
                throw new CouldNotSaveException(__('We can\'t save the cron expression.'));
            }
        }

        return parent::afterSave();
    }

    /**
     * Clean up old configuration entries from default group
     *
     * @return void
     */
    private function cleanupOldConfig(): void
    {
        try {
            $oldCronStringPath = 'crontab/default/jobs/comave_payout_management_sync/schedule/cron_expr';
            $oldCronModelPath = 'crontab/default/jobs/comave_payout_management_sync/run/model';

            $oldCronString = $this->configValueFactory->create()->load($oldCronStringPath, 'path');
            if ($oldCronString->getId()) {
                $oldCronString->delete();
                $this->_logger->info('Removed old cron string config', ['path' => $oldCronStringPath]);
            }

            $oldCronModel = $this->configValueFactory->create()->load($oldCronModelPath, 'path');
            if ($oldCronModel->getId()) {
                $oldCronModel->delete();
                $this->_logger->info('Removed old cron model config', ['path' => $oldCronModelPath]);
            }

        } catch (\Exception $e) {
            $this->_logger->warning('Error cleaning up old config: ' . $e->getMessage());
        }
    }
}
